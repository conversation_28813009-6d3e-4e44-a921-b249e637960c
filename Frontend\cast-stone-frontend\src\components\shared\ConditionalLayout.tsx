'use client';

import { usePathname } from 'next/navigation';
import Header from '@/components/shared/Header/Header';
import Footer from '@/components/shared/Footer/Footer';

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

export default function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();
  
  // Check if current route is admin-related
  const isAdminRoute = pathname.startsWith('/admin');

  if (isAdminRoute) {
    // For admin routes, don't show header/footer
    return <>{children}</>;
  }

  // For regular routes, show header and footer
  return (
    <>
      <Header />
      <main className="min-h-screen">
        {children}
      </main>
      <Footer />
    </>
  );
}
