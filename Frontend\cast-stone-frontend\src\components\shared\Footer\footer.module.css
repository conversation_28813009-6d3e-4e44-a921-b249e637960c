/* Footer Styles - Navy Blue Theme */
.footer {
  background: #1e3a8a;
  color: #ffffff;
  padding: 4rem 0 2rem;
  position: relative;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Content Grid */
.content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
  padding-bottom: 3rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Brand Section */
.brand {
  max-width: 400px;
}

.brandName {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.brandDescription {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
  font-weight: 400;
}

/* Social Links */
.socialLinks {
  display: flex;
  gap: 1rem;
}

.socialLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.socialLink:hover {
  background: #ffffff;
  color: #1e3a8a;
  transform: translateY(-2px);
}

/* Link Groups */
.linkGroup {
  display: flex;
  flex-direction: column;
}

.linkGroupTitle {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1.5rem;
  letter-spacing: -0.01em;
}

.linkList {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.link {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 400;
  line-height: 1.4;
}

.link:hover {
  color: #ffffff;
  transform: translateX(4px);
}

/* Contact Info */
.contactInfo {
  grid-column: 1 / -1;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.contactTitle {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1.5rem;
  letter-spacing: -0.01em;
}

.contactDetails {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.contactItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.contactLabel {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.85rem;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.contactValue {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  font-weight: 400;
}

/* Bottom Section */
.bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.legalLinks {
  display: flex;
  gap: 2rem;
}

.legalLink {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 400;
}

.legalLink:hover {
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer {
    padding: 3rem 0 1.5rem;
  }

  .container {
    padding: 0 1.5rem;
  }

  .content {
    grid-template-columns: 1fr 1fr;
    gap: 2.5rem;
  }

  .brand {
    grid-column: 1 / -1;
    max-width: none;
    text-align: center;
    margin-bottom: 1rem;
  }

  .socialLinks {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 2.5rem 0 1rem;
  }

  .container {
    padding: 0 1rem;
  }

  .content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .brand {
    margin-bottom: 1.5rem;
  }

  .brandName {
    font-size: 1.75rem;
  }

  .brandDescription {
    font-size: 0.9rem;
  }

  .linkGroup {
    align-items: center;
  }

  .linkGroupTitle {
    font-size: 1.125rem;
    margin-bottom: 1rem;
  }

  .contactInfo {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    text-align: center;
  }

  .contactDetails {
    grid-template-columns: 1fr;
    gap: 1rem;
    text-align: center;
  }

  .bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .legalLinks {
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 2rem 0 1rem;
  }

  .brandName {
    font-size: 1.5rem;
  }

  .brandDescription {
    font-size: 0.85rem;
  }

  .socialLinks {
    gap: 0.75rem;
  }

  .socialLink {
    width: 36px;
    height: 36px;
  }

  .linkGroupTitle {
    font-size: 1rem;
  }

  .link {
    font-size: 0.85rem;
  }

  .contactTitle {
    font-size: 1rem;
  }

  .contactLabel {
    font-size: 0.8rem;
  }

  .contactValue {
    font-size: 0.85rem;
  }

  .legalLinks {
    flex-direction: column;
    gap: 0.5rem;
  }
}
