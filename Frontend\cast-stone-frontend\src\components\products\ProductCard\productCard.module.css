/* Product Card Styles - Navy Blue Theme */
.productCard {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(30, 58, 138, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.productCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(30, 58, 138, 0.15);
}

/* Image Container */
.imageContainer {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.productImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.productCard:hover .productImage {
  transform: scale(1.05);
}

.outOfStockOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Product Info */
.productInfo {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.productName {
  color: #000000;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 0.75rem 0;
  line-height: 1.3;
}

.productDescription {
  color: #000000;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 1rem 0;
  flex: 1;
}

/* Price Container */
.priceContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.price {
  color: #000000;
  font-size: 1.5rem;
  font-weight: 700;
}

.collection {
  color: #000000;
  font-size: 0.85rem;
  font-style: italic;
}

/* Stock Info */
.stockInfo {
  margin-bottom: 1rem;
}

.inStock {
  color: #059669;
  font-size: 0.85rem;
  font-weight: 600;
}

.outOfStock {
  color: #dc2626;
  font-size: 0.85rem;
  font-weight: 600;
}

/* Action Buttons */
.actionButtons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: auto;
}

.viewDetailsBtn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  background: transparent;
  color: #1e3a8a;
  border: 2px solid #1e3a8a;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.viewDetailsBtn:hover {
  background: #1e3a8a;
  color: white;
}

/* Add to Cart Section */
.addToCartSection {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quantitySelector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.quantityBtn {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background: white;
  color: #000000;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quantityBtn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #1e3a8a;
}

.quantityBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity {
  min-width: 40px;
  text-align: center;
  font-weight: 600;
  color: #000000;
}

.addToCartBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem 1rem;
  background: #1e3a8a;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.addToCartBtn:hover:not(:disabled) {
  background: #3b82f6;
  transform: translateY(-1px);
}

.addToCartBtn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.cartIcon {
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

.loading {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.loading::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .productCard {
    margin-bottom: 1rem;
  }
  
  .imageContainer {
    height: 200px;
  }
  
  .productInfo {
    padding: 1rem;
  }
  
  .productName {
    font-size: 1.1rem;
  }
  
  .price {
    font-size: 1.25rem;
  }
  
  .actionButtons {
    gap: 0.5rem;
  }
  
  .addToCartSection {
    flex-direction: column;
  }
}
