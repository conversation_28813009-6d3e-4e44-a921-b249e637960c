/* Categories Section Styles */
.categoriesSection {
  padding: 6rem 0;
  background: linear-gradient(135deg, #faf9f7 0%, #ffffff 100%);
  position: relative;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Header Styles */
.header {
  text-align: center;
  margin-bottom: 4rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.sectionTitle {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 3rem;
  font-weight: 700;
  color: #4a3728;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.sectionSubtitle {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.125rem;
  color: #8b7355;
  line-height: 1.6;
  font-weight: 400;
}

/* Grid Layout */
.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Category Card Styles */
.categoryCard {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0 8px 32px rgba(74, 55, 40, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 400px;
}

.categoryCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(74, 55, 40, 0.15);
}

.cardLink {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: inherit;
  position: relative;
}

/* Image Styles */
.imageContainer {
  position: relative;
  height: 60%;
  overflow: hidden;
}

.categoryImage {
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.categoryCard:hover .categoryImage {
  transform: scale(1.05);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(74, 55, 40, 0.4) 100%
  );
  z-index: 1;
}

/* Card Content */
.cardContent {
  position: relative;
  height: 40%;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
}

.cardHeader {
  margin-bottom: 1rem;
}

.stats {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.statsNumber {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 2rem;
  font-weight: 700;
  color: #4a3728;
  line-height: 1;
}

.statsLabel {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.75rem;
  font-weight: 500;
  color: #8b7355;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.categoryTitle {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: #4a3728;
  margin: 0.25rem 0;
  line-height: 1.2;
}

.categorySubtitle {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.8rem;
  font-weight: 600;
  color: #8b7355;
  text-transform: uppercase;
  letter-spacing: 0.15em;
  margin: 0;
}

.categoryDescription {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 1rem;
  flex-grow: 1;
}

/* Card Actions */
.cardActions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.actionButton,
.secondaryButton {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.actionButton {
  background: #1e3a8a;
  color: #ffffff;
  padding: 0.5rem 1rem;
}

.actionButton:hover {
  background: #3b82f6;
  transform: translateY(-1px);
}

.secondaryButton {
  background: transparent;
  color: #8b7355;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgba(139, 115, 85, 0.3);
}

.secondaryButton:hover {
  background: rgba(139, 115, 85, 0.1);
  color: #4a3728;
}

/* Hover Effect */
.hoverEffect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(74, 55, 40, 0.05) 0%,
    transparent 50%,
    rgba(139, 115, 85, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.categoryCard:hover .hoverEffect {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .categoriesSection {
    padding: 4rem 0;
  }
  
  .container {
    padding: 0 1.5rem;
  }
  
  .sectionTitle {
    font-size: 2.5rem;
  }
  
  .grid {
    gap: 1.5rem;
  }
  
  .categoryCard {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .categoryCard {
    height: 300px;
  }
  
  .sectionTitle {
    font-size: 2rem;
  }
  
  .sectionSubtitle {
    font-size: 1rem;
  }
  
  .cardContent {
    padding: 1.25rem;
  }
  
  .statsNumber {
    font-size: 1.75rem;
  }
  
  .categoryTitle {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .categoriesSection {
    padding: 3rem 0;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .header {
    margin-bottom: 2.5rem;
  }
  
  .categoryCard {
    height: 280px;
  }
  
  .cardContent {
    padding: 1rem;
  }
  
  .cardActions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .actionButton,
  .secondaryButton {
    width: 100%;
    justify-content: center;
  }
}
