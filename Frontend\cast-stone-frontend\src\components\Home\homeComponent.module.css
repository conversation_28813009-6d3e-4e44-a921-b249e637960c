/* Home Component Styles - Navy Blue Theme */
.homeComponent {
  min-height: 100vh;
  background: #ffffff;
  overflow-x: hidden;
}

/* Smooth scrolling for the entire page */
.homeComponent {
  scroll-behavior: smooth;
}

/* Ensure proper spacing between sections */
.homeComponent > * {
  position: relative;
  z-index: 1;
}

/* Add subtle transitions for section reveals */
.homeComponent section {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .homeComponent {
    overflow-x: hidden;
  }
}

/* Print styles */
@media print {
  .homeComponent {
    background: white;
    color: #1e3a8a;
  }
}
